import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { InvoiceRecordSchema } from './schema/fhir_schema/invoice.schema.fhir.js';

// Load environment variables
dotenv.config();

// Configuration
const CONFIG = {
  // Default: Delete records older than 90 days
  DEFAULT_DAYS_OLD: 5,
  // Batch size for processing
  BATCH_SIZE: 100,
  // Dry run mode (set to false to actually delete)
  DRY_RUN: false
};

class InvoiceRecordCleanup {
  constructor() {
    this.db = null;
    this.InvoiceRecordModel = null;
  }

  async connect() {
    try {
      // Get MongoDB URI from environment
      const portalDbConnectionString = process.env.MONGODB_PORTAL_URI;
      if (!portalDbConnectionString) {
        throw new Error('❌ MONGODB_PORTAL_URI is not defined in environment variables');
      }

      console.log('🔌 Connecting to database...');
      this.db = mongoose.createConnection(portalDbConnectionString, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
      });

      // Register the InvoiceRecord model
      this.InvoiceRecordModel = this.db.model('InvoiceReportFHIRRecord', InvoiceRecordSchema);
      
      console.log('✅ Database connected successfully');
      return true;
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      return false;
    }
  }

  async disconnect() {
    if (this.db && this.db.readyState === 1) {
      await this.db.close();
      console.log('🔌 Database connection closed');
    }
  }

  calculateCutoffDate(daysOld = CONFIG.DEFAULT_DAYS_OLD) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);
    return cutoffDate;
  }

  async countOldRecords(daysOld = CONFIG.DEFAULT_DAYS_OLD) {
    try {
      const cutoffDate = this.calculateCutoffDate(daysOld);
      
      // Count records where created date is older than cutoff
      // Using $or to handle different possible date field structures
      const query = {
        $or: [
          { 'general.created': { $lt: cutoffDate } },
          { createdAt: { $lt: cutoffDate } },
          { _id: { $lt: mongoose.Types.ObjectId.createFromTime(cutoffDate.getTime() / 1000) } }
        ]
      };

      const count = await this.InvoiceRecordModel.countDocuments(query);
      
      console.log(`📊 Found ${count} invoice records older than ${daysOld} days (before ${cutoffDate.toISOString()})`);
      return { count, cutoffDate, query };
    } catch (error) {
      console.error('❌ Error counting old records:', error.message);
      throw error;
    }
  }

  async findOldRecords(daysOld = CONFIG.DEFAULT_DAYS_OLD, limit = 10) {
    try {
      const { query } = await this.countOldRecords(daysOld);
      
      const records = await this.InvoiceRecordModel
        .find(query)
        .limit(limit)
        .select('fhirId general.created patient.name patient.id createdAt _id')
        .lean();

      console.log(`📋 Sample of old records (showing first ${Math.min(limit, records.length)}):`);
      records.forEach((record, index) => {
        const createdDate = record.general?.created || record.createdAt || 'Unknown';
        const patientName = record.patient?.name || 'Unknown Patient';
        const patientId = record.patient?.id || 'Unknown ID';
        
        console.log(`  ${index + 1}. FHIR ID: ${record.fhirId}`);
        console.log(`     Patient: ${patientName} (ID: ${patientId})`);
        console.log(`     Created: ${createdDate}`);
        console.log(`     MongoDB ID: ${record._id}`);
        console.log('');
      });

      return records;
    } catch (error) {
      console.error('❌ Error finding old records:', error.message);
      throw error;
    }
  }

  async deleteOldRecords(daysOld = CONFIG.DEFAULT_DAYS_OLD, dryRun = CONFIG.DRY_RUN) {
    try {
      const { count, cutoffDate, query } = await this.countOldRecords(daysOld);
      
      if (count === 0) {
        console.log('✅ No old records found to delete');
        return { deleted: 0, errors: [] };
      }

      if (dryRun) {
        console.log(`🔍 DRY RUN MODE: Would delete ${count} records older than ${daysOld} days`);
        console.log('   Set dryRun=false to actually perform deletion');
        return { deleted: 0, errors: [], dryRun: true };
      }

      console.log(`⚠️  DANGER: About to delete ${count} invoice records older than ${daysOld} days`);
      console.log(`   Cutoff date: ${cutoffDate.toISOString()}`);
      
      // In a real scenario, you might want to add a confirmation prompt here
      // For now, we'll proceed with the deletion

      let totalDeleted = 0;
      let errors = [];
      let processed = 0;

      // Process in batches to avoid memory issues
      while (processed < count) {
        try {
          const batch = await this.InvoiceRecordModel
            .find(query)
            .limit(CONFIG.BATCH_SIZE)
            .select('_id fhirId');

          if (batch.length === 0) break;

          const batchIds = batch.map(record => record._id);
          const deleteResult = await this.InvoiceRecordModel.deleteMany({
            _id: { $in: batchIds }
          });

          totalDeleted += deleteResult.deletedCount;
          processed += batch.length;

          console.log(`🗑️  Deleted batch: ${deleteResult.deletedCount}/${batch.length} records (Total: ${totalDeleted}/${count})`);
          
          // Small delay to prevent overwhelming the database
          await new Promise(resolve => setTimeout(resolve, 100));
          
        } catch (batchError) {
          console.error(`❌ Error deleting batch starting at ${processed}:`, batchError.message);
          errors.push({
            batch: Math.floor(processed / CONFIG.BATCH_SIZE) + 1,
            error: batchError.message
          });
          processed += CONFIG.BATCH_SIZE; // Skip this batch
        }
      }

      console.log(`✅ Deletion completed: ${totalDeleted} records deleted`);
      if (errors.length > 0) {
        console.log(`⚠️  Errors encountered: ${errors.length} batches failed`);
        errors.forEach(error => {
          console.log(`   Batch ${error.batch}: ${error.error}`);
        });
      }

      return { deleted: totalDeleted, errors };
    } catch (error) {
      console.error('❌ Error during deletion process:', error.message);
      throw error;
    }
  }

  async getRecordStats() {
    try {
      const total = await this.InvoiceRecordModel.countDocuments();
      const lastDays = await this.countOldRecords();
      const last30Days = await this.countOldRecords(30);
      const last90Days = await this.countOldRecords(90);
      const last180Days = await this.countOldRecords(180);
      const last365Days = await this.countOldRecords(365);

      console.log('📈 Invoice Record Statistics:');
      console.log(`   Total records: ${total}`);
      console.log(`   Older than ${CONFIG.DEFAULT_DAYS_OLD} days: ${lastDays.count}`);
      console.log(`   Older than 30 days: ${last30Days.count}`);
      console.log(`   Older than 90 days: ${last90Days.count}`);
      console.log(`   Older than 180 days: ${last180Days.count}`);
      console.log(`   Older than 365 days: ${last365Days.count}`);

      return {
        total,
          lastDays: lastDays.count,
        last30Days: last30Days.count,
        last90Days: last90Days.count,
        last180Days: last180Days.count,
        last365Days: last365Days.count
      };
    } catch (error) {
      console.error('❌ Error getting record statistics:', error.message);
      throw error;
    }
  }
}

// Main execution function
async function main() {
  const cleanup = new InvoiceRecordCleanup();
  
  try {
    // Connect to database
    const connected = await cleanup.connect();
    if (!connected) {
      process.exit(1);
    }

    console.log('🧹 Invoice Record Cleanup Tool');
    console.log('================================\n');

    // Get overall statistics and log them
    const stats = await cleanup.getRecordStats();
    console.log('');

    // Show sample of old records using CONFIG.DEFAULT_DAYS_OLD
    console.log(`📋 Sample of records to be cleaned (${CONFIG.DEFAULT_DAYS_OLD}+ days old):`);
    await cleanup.findOldRecords(CONFIG.DEFAULT_DAYS_OLD, 5);

    // Perform cleanup using configured values
    console.log('🗑️  Starting cleanup process...');
    const result = await cleanup.deleteOldRecords(CONFIG.DEFAULT_DAYS_OLD, CONFIG.DRY_RUN);
    
    if (result.dryRun) {
      console.log('\n💡 To actually delete records, set CONFIG.DRY_RUN = false in the script');
    }

  } catch (error) {
    console.error('💥 Script execution failed:', error.message);
    process.exit(1);
  } finally {
    await cleanup.disconnect();
  }
}

// Export for use as module or run directly
export { InvoiceRecordCleanup };

// Run if this file is executed directly
// Debug: Check if we should run main
console.log('Debug: import.meta.url =', import.meta.url);
console.log('Debug: process.argv[1] =', process.argv[1]);
console.log('Debug: file:// + process.argv[1] =', `file://${process.argv[1]}`);

// Alternative check for direct execution
const isMainModule = import.meta.url === `file://${process.argv[1]}` ||
                    import.meta.url.endsWith(process.argv[1]) ||
                    process.argv[1].endsWith('cleanup-old-invoice-records.js');

console.log('Debug: isMainModule =', isMainModule);

if (isMainModule) {
  console.log('🚀 Running script directly...');
  main();
} else {
  console.log('📦 Script imported as module');
}
