import { Schema } from 'mongoose';

const telecomSchema = new Schema({
    system: String,
    value: String,
    use: String
});

const licenseSchema = new Schema({
    code: String,
    display: String,
    licNo: String
});

const allergyIntoleranceSchema = new Schema({
    type: String,
    clinicalStatus: String,
    verificationStatus: String,
    doctor: String,
    notes: [String]
});

const conditionSchema = new Schema({
    type: String,
    status: String,
    recordedDate: String,
    startDate:String,
    endDate:String
});

const serviceRequestSchema = new Schema({
    status: String,
    intent: String,
    categories: [String],
    type: String
});

const dosageInstructionSchema = new Schema({
    text: String,
    additionalInstruction: String,
    route: String,
    repeat: {
        frequency: Number,
        period: Number,
        periodUnit: String
    },
    doseQuantity:{
        value:String,
        unit:String
    },
    site:String
    // method: String
});

const medicationRequestSchema = new Schema({
    status: String,
    intent: String,
    authoredOn: String,
    medication: String,
    forCondition: [String],
    reason:[String],
    dosageInstruction: [dosageInstructionSchema]
});

const medicationStatementSchema = new Schema({
    status: String,
    type: String
});

const procedureSchema = new Schema({
    status: String,
    type: String,
    performedDateTime: String,
    followUp: [String]
});

const attachmentSchema = new Schema({
    contentType: String,
    language: String,
    data: String,
    title: String,
    creation: String
});

const documentReferenceSchema = new Schema({
    status: String,
    docStatus: String,
    type: String,
    content: [{ attachment: attachmentSchema }]
});

const appointmentSchema = new Schema({
    status: String,
    serviceCategories: [String],
    serviceTypes: [String],
    specialty: [String],
    appointmentType: String,
    description: String,
    start: String,
    end: String,
    created: String,
    reasonReference: [String],
    basedOnServices: [String]
});

const familyHistorySchema = new Schema({
    name: String,
    duration: {
        value: Number,
        unit: String
    },
    notes: String
});

const signatureSchema = new Schema({
    who: {
        type: { type: String },
        name: String
    },
    sigFormat: String,
    data: String
});

const addressSchema = new Schema({
    use: String,
    type: String,
    text: String,
    // line: [String],
    city: String,
    state: String,
    district: String,
    postalCode: String,
    country: String
});

const patientSchema = new Schema({
    id:String,
    abhaNumber: String,
    abhaAddress: String,
    name: {
        text:String,
        prefix:[String]
    },
    gender: String,
    dob: String,
    doctors: [String],
    allergyIntolerances: [allergyIntoleranceSchema],
    telecom: [telecomSchema],
    address:[addressSchema]
});

const generalSchema = new Schema({
    artifact: String,
    hipUrl: String,
    hipIds: [String],
    status: String,
    clientId: String
});

const practitionerSchema = new Schema({
    names: [String],
    licenses: [licenseSchema],
    telecom: [telecomSchema],
    gender:String,
    birthDate:String,
    patient: String,
    address:[addressSchema],
});

const encounterSchema = new Schema({
    status: String,
    startTime: String,
    endTime: String
});


const organizationSchema = new Schema({
    name: String,
    telecom: [telecomSchema],
    licenses: [licenseSchema]
});

const OPConsultRecordSchema = new Schema({
    fhirId: {
        type: String,
        required: true,
        index: true
    },
    general: generalSchema,
    patient: patientSchema,
    practitioners: [practitionerSchema],
    encounter: encounterSchema,
    organization: organizationSchema,
    conditions: [conditionSchema],
    serviceRequests: [serviceRequestSchema],
    medicationStatements: [medicationStatementSchema],
    medicationRequests: [medicationRequestSchema],
    procedures: [procedureSchema],
    familyHistory: [familyHistorySchema],
    documentReferences: [documentReferenceSchema],
    appointment: appointmentSchema,
    signature: signatureSchema,
    abhaCareContextLinked:{
        type: Boolean,
        default: false
    }
});



export { OPConsultRecordSchema };
